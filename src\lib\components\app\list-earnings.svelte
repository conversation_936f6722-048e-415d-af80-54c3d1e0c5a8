<script lang="ts">
  import { Sun, Moon } from "lucide-svelte";

  let props = $props();
  let showEarnings = $state(props.showEarnings);

  const extractNumbers = (text: string) => {
    if (!text) return "";
    return text.replace(/[^\d.-]/g, "");
  };

  const isGreater = (a: string, b: string) => {
    const numA = parseFloat(extractNumbers(a));
    const numB = parseFloat(extractNumbers(b));

    if (isNaN(numA) || isNaN(numB)) return "";

    if (numA > numB) return "text-green-500";
    if (numA < numB) return "text-red-500";
    return "";
  };

  const parseFloatSafe = (val: string): number => parseFloat(val?.replace(/[^\d.-]/g, "")) || 0;

  const getSurprise = (eps: string, forecast: string): string => {
    const epsNum = parseFloatSafe(eps);
    const forecastNum = parseFloatSafe(forecast);
    if (!eps || !forecast || forecastNum === 0) return "";
    const result = ((epsNum - forecastNum) / forecastNum) * 100;
    return result.toFixed(2) + "%";
  };

  const getGrowth = (rev: string, forecast: string): string => {
    const revNum = parseFloatSafe(rev);
    const forecastNum = parseFloatSafe(forecast);
    if (!rev || !forecast || forecastNum === 0) return "";
    const result = ((revNum - forecastNum) / forecastNum) * 100;
    return result.toFixed(2) + "%";
  };

  const getSignal = (epsSurprise: string, revGrowth: string): string => {
    const eps = parseFloat(epsSurprise.replace("%", ""));
    const rev = parseFloat(revGrowth.replace("%", ""));

    if (!epsSurprise || !revGrowth || isNaN(eps) || isNaN(rev)) return "bg-gray-300";

    if (eps >= 10 && rev >= 5) return "bg-green-500";
    if (eps <= -10 && rev <= -5) return "bg-red-500";

    return "bg-gray-300";
  };
</script>

<div class="relative w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <h2 class="text-sm leading-none font-medium uppercase">{props.title}</h2>
    <div class="absolute top-[5px] right-2.5 left-auto flex items-center text-sm leading-none">
      <button
        class={`cursor-pointer rounded-l-md p-1.5 text-center text-xs leading-none font-normal uppercase ring-1 ring-inset ${showEarnings ? "bg-white/10 text-gray-200 ring-white/20" : "bg-gray-400/10 text-gray-400 ring-gray-400/20"}`}
        type="button"
        onclick={() => (showEarnings = !showEarnings)}>Economic</button
      >
      <button
        class={`cursor-pointer rounded-r-md p-1.5 text-center text-xs leading-none font-normal uppercase ring-1 ring-inset ${showEarnings ? "bg-white/10 text-gray-200 ring-white/20" : "bg-gray-400/10 text-gray-400 ring-gray-400/20"}`}
        type="button"
        onclick={() => (showEarnings = !showEarnings)}>Earnings</button
      >
    </div>
  </div>
  {#if props?.items?.length === 0 || props?.items === null}
    <svg
      class="mx-auto mt-6 size-10 animate-spin text-white"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
      ></circle><path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path></svg
    >
  {:else}
    <div class="-mx-1.5 w-full overflow-y-auto xl:h-[255px]">
      <table class="w-full table-fixed tracking-tighter">
        <thead class="text-center text-xs uppercase">
          <tr>
            <th class="w-[16px] truncate py-2 leading-none font-medium"></th>
            <th class="w-2/12 truncate p-2 leading-none font-medium"></th>
            <th class="w-4/12 truncate p-2 text-right leading-none font-medium">Act / Est</th>
            <th class="w-2/12 truncate p-2 leading-none font-medium">Surprise</th>
            <th class="w-4/12 truncate p-2 text-right leading-none font-medium">Rev / Est</th>
            <th class="w-2/12 truncate p-2 leading-none font-medium">Growth</th>
            <th class="w-2/12 truncate p-2 text-right leading-none font-medium">Mkt Cap</th>
            <th class="w-[16px] truncate py-2 leading-none font-medium"></th>
          </tr>
        </thead>
        <tbody class="text-center text-sm">
          {#each props.items as item}
            <tr>
              <td class="py-1.5 leading-none font-normal">
                <div class="flex items-center gap-1">
                  {#if item.time == 1}
                    <Sun size="16" />
                  {:else if item.time == 3}
                    <Moon size="16" />
                  {/if}
                </div>
              </td>
              <td class="truncate px-2 py-1.5 text-left leading-none font-normal">
                <div class="flex items-center gap-1">
                  <img
                    src="/icons/{item.flag == 'United States' ? 'US' : 'white'}.svg"
                    alt={item.flag}
                    class="size-4 rounded-xs select-none"
                    width="16"
                    height="16"
                  />
                  <span>{item.symbol}</span>
                  <!-- <span>{item.company}</span> -->
                </div>
              </td>
              <td class="px-2 py-1.5 text-right leading-none font-normal">
                <span
                  class={item.epsActual == "" ? "" : isGreater(item.epsActual, item.epsForecast)}
                  >{item.epsActual == "" ? "--" : item.epsActual}</span
                >
                / {item.epsForecast == "" ? "--" : item.epsForecast}
              </td>
              <td class="px-2 py-1.5 leading-none font-normal">
                <span
                  class={item.epsForecast == ""
                    ? ""
                    : isGreater(getSurprise(item.epsActual, item.epsForecast), "0")}
                  >{getSurprise(item.epsActual, item.epsForecast)}</span
                >
              </td>
              <td class="px-2 py-1.5 text-right leading-none font-normal">
                <span
                  class={item.revActual == "" ? "" : isGreater(item.revActual, item.revForecast)}
                  >{item.revActual == "" ? "--" : item.revActual}</span
                >
                / {item.revForecast == "" ? "--" : item.revForecast}
              </td>
              <td class="px-2 py-1.5 leading-none font-normal">
                <span
                  class={item.revForecast == ""
                    ? ""
                    : isGreater(getGrowth(item.revActual, item.revForecast), "0")}
                  >{getGrowth(item.revActual, item.revForecast)}</span
                >
              </td>
              <td class="px-2 py-1.5 text-right leading-none font-normal">{item.marketcap}</td>
              <td class="py-1.5 leading-none font-normal">
                <div
                  class={`inline-block h-3 w-3 rounded-xs ${getSignal(
                    getSurprise(item.epsActual, item.epsForecast),
                    getGrowth(item.revActual, item.revForecast)
                  )}`}
                ></div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>
